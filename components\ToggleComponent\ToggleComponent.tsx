import React, { useEffect, useState } from "react";
import ToggleSwitch from "toggle-switch-react-native";
import useSetApiData from "../../hooks/useSetApiData";
import useTenStackMutateD from "@/hook/useTenStackMutateD/useTenStackMutate";
import { Text, View } from "react-native";
import { useDastBoard } from "../../store/Auth/Dashboard/Dashboardstore";

interface Props {
  setToggle: React.Dispatch<React.SetStateAction<boolean>>;
}

const ToggleComponent = (props: Props) => {
  const [Toggle, setToggle] = useState(false);
  const {mutate: SetFunction} = useTenStackMutateD({
    endpoint: "auth/shop_status_change",
    invalidateQueriesKey: ["dashboard"],
  });
  const shopId = useDastBoard((state) => state.shopId);
  const is_shop_open = useDastBoard((state) => state.dashboardData?.is_shop_open);
  useEffect(() => {
    if (is_shop_open !== undefined) {
      setToggle(is_shop_open);
    }
  }, []);
  useEffect(() => {
    props.setToggle(Toggle);
  }, [Toggle]);

  return (
    <View className="items-start">
      <ToggleSwitch
        isOn={Toggle}
        onColor="#28C979"
        offColor="#D13434"
        size="medium"
        onToggle={() => {
          SetFunction(
            {
              shop_id: shopId,
              type: Toggle ? 0 : 1,
            },
            {
              onSuccess: (data) => {
                setToggle(!Toggle);
              },
              onError: (error) => {
                console.log(error);
              },
            },
          );
        }}
      />
      <Text
        className="font-[500] text-[14px] leading-[16px] mt-1"
        style={{
          color: Toggle ? "#28C979" : "#D13434",
        }}
      >
        {Toggle ? "Shop Open" : "Shop Closed"}
      </Text>
    </View>
  );
};

export default ToggleComponent;
