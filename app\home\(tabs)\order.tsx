import AsyncStorage from "@react-native-async-storage/async-storage";
import React, {useCallback} from "react";
import ShopHeader from "@/components/ShopHead/ShopHeader";
import TableComponent from "@/component/TableComponent";
import {LinearGradient} from "expo-linear-gradient";
import {router} from "expo-router";
import {useEffect, useState} from "react";
import {ActivityIndicator, Alert, FlatList} from "react-native";
import {SlideTabsHome} from "@/component/SlideTabs";
import {useStatusCategory} from "@/store";
import {useDastBoard} from "@/store/Auth/Dashboard/Dashboardstore";

import {
  View,
  Text,
  TouchableOpacity,
  Image,
  RefreshControl,
  DeviceEventEmitter,
} from "react-native";

const BannerImg = require("../../../assets/Img/bannerIma.png");
export const GetMailAndpassWord = async () => {
  let email = await AsyncStorage.getItem("email");
  let password = await AsyncStorage.getItem("password");
  let type = await AsyncStorage.getItem("type");
  return {email, password, type};
};

const Orders = () => {
  const [ActiveFilterTab, setActiveFilterTab] = useState(0);
  const {FilterTab, setFilterTab} = useStatusCategory((data) => data);
  const isLoading = useDastBoard((state) => state.isLoading);
  const getOrderStatus = useDastBoard((state) => state.getOrderStatus);
  const dashboardData = useDastBoard((state) => state.dashboardData);
  const loadDashBoard = useDastBoard((state) => state.loadDashBoard);

  const handleCustomEvent = () => {
    loadDashBoard();
  };

  useEffect(() => {
    const listener = DeviceEventEmitter.addListener("orderStatusChanged", handleCustomEvent);
    return () => {
      listener.remove();
    };
  }, []);

  useEffect(() => {
    loadDashBoard();
  }, []);
  const memoizedlistHeaderComponent = useCallback(() => {
    return (
      <View className="flex-1 bg-white">
        <View className="">
          {/* Banner Area  */}
          <View className="h-[80px] items-center justify-center relative">
            <LinearGradient
              colors={["#006634", "#fff"]}
              start={[0, 0.2]}
              end={[0, 3]}
              locations={[0, 1]}
              style={{
                position: "absolute",
                left: 0,
                right: 0,
                top: 0,
                bottom: 0,
                zIndex: 1,
                flex: 1,
              }}
            />
            <Image source={BannerImg} className="w-full z-20" style={{objectFit: "contain"}} />
          </View>
        </View>
        {/* Header Area */}
        <ShopHeader />

        {/* Content Area */}
        <View className="px-3 mt-6">
          {/* I want to see Text Area */}
          <View>
            <Text className="font-[400] text-[#001A03] text-[16px] leading-[24px]">
              I want to see
            </Text>
          </View>
          {/* slideTab Area */}
          <View className="mt-2">
            <SlideTabsHome
              data={FilterTab}
              setActivefun={setActiveFilterTab}
              Active={ActiveFilterTab}
            />
          </View>
        </View>

        <View className="mt-8">
          {/* Table Head Area */}
          <View className="flex-row bg-[#F5FFF6]">
            <View className="flex-1 justify-center items-center mr-5">
              <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">Order ID</Text>
            </View>
            <View className="flex-1 justify-center items-center mr-4">
              <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">Value</Text>
            </View>
            <View className="flex-1 justify-center items-center mr-2">
              <Text className="font-[400] text-[14px] leading-[21px] text-[#00660A]">Actions</Text>
            </View>
          </View>
          {/* Table Column Area */}
        </View>
      </View>
    );
  }, []);

  return (
    <>
      {isLoading ? (
        <View className="flex-1 bg-white min-h-[100vh]">
          <ActivityIndicator />
        </View>
      ) : (
        <View className="flex-1 bg-white min-h-[100vh]">
          <FlatList
            refreshControl={
              <RefreshControl
                refreshing={isLoading}
                onRefresh={() => {
                  loadDashBoard();
                }}
              />
            }
            ListHeaderComponent={memoizedlistHeaderComponent}
            showsVerticalScrollIndicator={false}
            data={dashboardData?.order_list}
            keyExtractor={({order_number}) => order_number}
            ListEmptyComponent={() => {
              return (
                <View className="items-center justify-center flex-1 mt-10">
                  <Text className="text-[20px] font-[600]">No Order Available</Text>
                </View>
              );
            }}
            renderItem={({item: {order_id, order_number, order_status, total_amnt}}) => {
              if (order_status === ActiveFilterTab || ActiveFilterTab === 0) {
                return (
                  <TouchableOpacity
                    key={order_id}
                    className="px-4 bg-white"
                    onPress={() => {
                      router.push({
                        pathname: `/home/<USER>/[query]`,
                        params: {
                          query: order_id,
                          id: order_id,
                          value: total_amnt,
                          actions: order_status,
                          orderId: order_number,
                        },
                      });
                    }}
                  >
                    <>
                      <TableComponent
                        key={order_id}
                        orderid={order_number}
                        value={total_amnt}
                        actions={getOrderStatus(order_status)}
                      />
                    </>
                  </TouchableOpacity>
                );
              }
              return null;
            }}
          />
        </View>
      )}
    </>
  );
};
export default Orders;
